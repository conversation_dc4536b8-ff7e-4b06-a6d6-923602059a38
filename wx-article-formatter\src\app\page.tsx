'use client';

import React, { useState } from 'react';
import RichTextEditor from '@/components/RichTextEditor';

export default function Home() {
  const [url, setUrl] = useState('');
  const [content, setContent] = useState('');
  const [richTextContent, setRichTextContent] = useState('');
  const [extractedData, setExtractedData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'url' | 'paste' | 'editor'>('url');
  const [selectedStyle, setSelectedStyle] = useState('classic');
  const [formattedContent, setFormattedContent] = useState('');
  const [availableStyles, setAvailableStyles] = useState<any[]>([]);
  const [originalityResult, setOriginalityResult] = useState<any>(null);
  const [isCheckingOriginality, setIsCheckingOriginality] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  // 获取可用的排版样式
  const fetchAvailableStyles = async () => {
    try {
      const response = await fetch('/api/format');
      if (response.ok) {
        const data = await response.json();
        setAvailableStyles(data.styles);
      }
    } catch (error) {
      console.error('获取样式失败:', error);
    }
  };

  // 组件加载时获取样式
  React.useEffect(() => {
    fetchAvailableStyles();
  }, []);

  // 处理图片URL，使用代理解决跨域问题
  const processImageUrl = (url: string) => {
    if (!url) return '';

    // 如果是相对路径或协议相对路径，跳过
    if (url.startsWith('/') && !url.startsWith('//')) {
      return url;
    }

    // 如果是协议相对路径，添加https
    if (url.startsWith('//')) {
      url = 'https:' + url;
    }

    // 使用代理URL来避免跨域问题
    return `/api/image-proxy?url=${encodeURIComponent(url)}`;
  };

  // 生成兼容性更好的富文本内容（用于复制到其他编辑器）
  const convertToCompatibleRichText = (data: any) => {
    let richContent = `
      <table style="width: 100%; max-width: 800px; margin: 0 auto; font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif; line-height: 1.8; color: #333; border-collapse: collapse;">
        <tr>
          <td style="padding: 0;">
            <!-- 标题区域 -->
            <table style="width: 100%; margin-bottom: 30px; border-collapse: collapse;">
              <tr>
                <td style="text-align: center;">
                  <h1 style="
                    font-size: 28px;
                    font-weight: bold;
                    margin: 20px 0 10px 0;
                    padding: 15px 25px;
                    background: #f0f8ff;
                    color: #2c3e50;
                    border: 3px solid #3498db;
                    display: inline-block;
                  ">${data.title || '文章标题'}</h1>`;

    // 副标题
    if (data.subtitle) {
      richContent += `
                  <h2 style="
                    font-size: 20px;
                    font-weight: bold;
                    color: #34495e;
                    margin: 15px 0 5px 0;
                    text-align: center;
                  ">${data.subtitle}</h2>`;
    }

    // 作者信息
    if (data.author) {
      richContent += `
                  <p style="
                    text-align: center;
                    font-size: 16px;
                    color: #7f8c8d;
                    margin: 10px 0 20px 0;
                  ">作者：${data.author}</p>`;
    }

    richContent += `
                </td>
              </tr>
            </table>`;

    // 摘要
    if (data.summary && data.hasSummary) {
      richContent += `
            <table style="width: 100%; background: #fff3cd; margin: 25px 0; border: 2px solid #ffc107; border-collapse: collapse;">
              <tr>
                <td style="padding: 20px;">
                  <h3 style="
                    font-size: 18px;
                    font-weight: bold;
                    color: #856404;
                    margin: 0 0 10px 0;
                    border-bottom: 2px solid #ffc107;
                    padding-bottom: 5px;
                    display: inline-block;
                  ">摘要</h3>
                  <p style="
                    margin: 0;
                    font-size: 15px;
                    color: #856404;
                    line-height: 1.7;
                    text-indent: 0;
                  ">${data.summary}</p>
                </td>
              </tr>
            </table>`;
    }

    // 处理正文内容
    let contentText = '';
    if (data.content && data.content.includes('<')) {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = data.content;
      const paragraphs = Array.from(tempDiv.querySelectorAll('p')).map(p => p.textContent?.trim()).filter(Boolean);
      contentText = paragraphs.join('\n\n');
    } else {
      contentText = data.content || '';
    }

    // 检查并移除已存在的版权声明，避免重复
    contentText = contentText.replace(/本网站作品著作权归作者本人所有，凡发表在网站的文章，未经作者本人授权，不得转载。/g, '').trim();

    // 正文内容区域
    richContent += `
            <table style="width: 100%; background: #f8f9fa; margin: 25px 0; border: 2px solid #3498db; border-collapse: collapse;">
              <tr>
                <td style="padding: 25px;">`;

    // 处理正文段落和图片
    const paragraphs = contentText.split('\n').filter(p => p.trim());
    const images = data.images || [];

    if (images.length > 0) {
      const imagePositions = [];
      const interval = Math.max(1, Math.floor(paragraphs.length / images.length));
      for (let i = 0; i < images.length; i++) {
        imagePositions.push(Math.min(paragraphs.length, (i + 1) * interval));
      }

      let imageIndex = 0;
      for (let i = 0; i <= paragraphs.length; i++) {
        if (imagePositions.includes(i) && imageIndex < images.length) {
          const proxiedImg = processImageUrl(images[imageIndex]);
          richContent += `
                  <table style="width: 100%; margin: 25px 0; border-collapse: collapse;">
                    <tr>
                      <td style="text-align: center;">
                        <img src="${proxiedImg}" alt="图片 ${imageIndex + 1}" style="
                          max-width: 100%;
                          height: auto;
                        " />
                      </td>
                    </tr>
                  </table>`;
          imageIndex++;
        }

        if (i < paragraphs.length) {
          richContent += `
                  <p style="
                    margin: 15px 0;
                    font-size: 16px;
                    line-height: 1.8;
                    color: #333;
                    text-indent: 0;
                  ">${paragraphs[i].trim()}</p>`;
        }
      }

      while (imageIndex < images.length) {
        const proxiedImg = processImageUrl(images[imageIndex]);
        richContent += `
                  <table style="width: 100%; margin: 25px 0; border-collapse: collapse;">
                    <tr>
                      <td style="text-align: center;">
                        <img src="${proxiedImg}" alt="图片 ${imageIndex + 1}" style="
                          max-width: 100%;
                          height: auto;
                        " />
                      </td>
                    </tr>
                  </table>`;
        imageIndex++;
      }
    } else {
      paragraphs.forEach(paragraph => {
        richContent += `
                  <p style="
                    margin: 15px 0;
                    font-size: 16px;
                    line-height: 1.8;
                    color: #333;
                    text-indent: 0;
                  ">${paragraph.trim()}</p>`;
      });
    }

    // 版权声明
    richContent += `
                  <p style="
                    margin: 30px 0 15px 0;
                    font-size: 14px;
                    color: #999;
                    text-align: left;
                    text-indent: 0;
                    border-top: 1px solid #eee;
                    padding-top: 15px;
                  ">本网站作品著作权归作者本人所有，凡发表在网站的文章，未经作者本人授权，不得转载。</p>
                </td>
              </tr>
            </table>`;

    // 编者按和作者简介区域
    if (data.editorNote || data.authorBio) {
      richContent += `
            <table style="width: 100%; background: #fff; margin: 25px 0; border: 2px solid #e9ecef; border-collapse: collapse;">
              <tr>
                <td style="padding: 25px;">`;

      if (data.editorNote) {
        let cleanEditorNote = data.editorNote;
        cleanEditorNote = cleanEditorNote.replace(/^【?编者按】?[：:]?\s*/i, '');
        cleanEditorNote = cleanEditorNote.replace(/^编者按[：:]?\s*/i, '');

        richContent += `
                  <h3 style="
                    font-size: 18px;
                    font-weight: bold;
                    color: #333;
                    margin: 0 0 10px 0;
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 5px;
                    display: inline-block;
                  ">编者按</h3>
                  <p style="
                    margin: 0 0 20px 0;
                    font-size: 15px;
                    color: #555;
                    line-height: 1.7;
                    text-indent: 0;
                  ">${cleanEditorNote}</p>`;
      }

      if (data.authorBio) {
        let cleanAuthorBio = data.authorBio;
        cleanAuthorBio = cleanAuthorBio.replace(/^作者[：:]?\s*/i, '');

        richContent += `
                  <h3 style="
                    font-size: 18px;
                    font-weight: bold;
                    color: #333;
                    margin: 0 0 15px 0;
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 5px;
                    display: inline-block;
                  ">作者简介</h3>
                  <table style="width: 100%; border-collapse: collapse;">
                    <tr>`;

        if (data.authorPhoto) {
          const proxiedPhoto = processImageUrl(data.authorPhoto);
          richContent += `
                      <td style="width: 80px; vertical-align: top; padding-right: 15px;">
                        <img src="${proxiedPhoto}" alt="${data.author}" style="
                          width: 80px;
                          height: 80px;
                          border-radius: 50%;
                          object-fit: cover;
                        " />
                      </td>`;
        }

        richContent += `
                      <td style="vertical-align: top;">
                        <p style="
                          margin: 0 0 5px 0;
                          font-size: 16px;
                          font-weight: bold;
                          color: #333;
                        ">${data.author}</p>
                        <p style="
                          margin: 0;
                          font-size: 15px;
                          color: #555;
                          line-height: 1.7;
                          text-indent: 0;
                        ">${cleanAuthorBio}</p>
                      </td>
                    </tr>
                  </table>`;
      }

      richContent += `
                </td>
              </tr>
            </table>`;
    }

    richContent += `
          </td>
        </tr>
      </table>`;

    return richContent;
  };

  // 生成随机的文章样式
  const generateArticleStyle = () => {
    const styles = [
      {
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        textColor: 'white',
        borderColor: '#667eea'
      },
      {
        background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        textColor: 'white',
        borderColor: '#f093fb'
      },
      {
        background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        textColor: 'white',
        borderColor: '#4facfe'
      },
      {
        background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        textColor: 'white',
        borderColor: '#43e97b'
      },
      {
        background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        textColor: 'white',
        borderColor: '#fa709a'
      }
    ];
    return styles[Math.floor(Math.random() * styles.length)];
  };

  // 将提取的内容转换为富文本格式
  const convertToRichText = (data: any) => {
    const style = generateArticleStyle();

    let richContent = `
      <div style="max-width: 800px; margin: 0 auto; font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif; line-height: 1.8; color: #333;">
        <!-- 标题区域 -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="
            font-size: 28px;
            font-weight: bold;
            margin: 20px 0 10px 0;
            padding: 15px 25px;
            background: ${style.background};
            color: ${style.textColor};
            border: 3px solid ${style.borderColor};
            display: inline-block;
          ">${data.title || '文章标题'}</h1>`;

    if (data.subtitle) {
      richContent += `
          <h2 style="
            font-size: 20px;
            font-weight: 500;
            margin: 15px 0 10px 0;
            color: #666;
          ">${data.subtitle}</h2>`;
    }

    richContent += `
          <p style="
            font-size: 16px;
            color: #888;
            margin: 10px 0 0 0;
            font-weight: 500;
          ">作者：${data.author || '未知作者'}</p>
        </div>`;

    // 摘要 - 只有当真正存在article-abs且有摘要内容时才显示
    if (data.hasSummary && data.summary && data.summary.trim()) {
      // 清理摘要内容，去掉重复的"摘要："
      let cleanSummary = data.summary.trim();
      cleanSummary = cleanSummary.replace(/^摘要[：:]?\s*/i, '');

      // 如果清理后还有内容，才显示摘要区域
      if (cleanSummary.trim()) {
        richContent += `
        <div style="
          background: #f8f9fa;
          padding: 20px;
          margin: 25px 0;
          border-left: 4px solid ${style.borderColor};
          border-radius: 0 8px 8px 0;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        ">
          <p style="
            margin: 0;
            font-size: 16px;
            color: #555;
            font-style: italic;
            text-indent: 0;
          "><strong>摘要：</strong>${cleanSummary}</p>
        </div>`;
      }
    }

    // 正文内容区域开始
    richContent += `
        <div style="
          background: #f8f9fa;
          padding: 25px;
          margin: 25px 0;
          border: 2px solid ${style.borderColor};
        ">`;

    // 处理正文内容
    let contentText = '';
    if (data.content && data.content.includes('<')) {
      // 如果是HTML格式，清理并提取文本
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = data.content;
      const paragraphs = Array.from(tempDiv.querySelectorAll('p')).map(p => p.textContent?.trim()).filter(Boolean);
      contentText = paragraphs.join('\n\n');
    } else {
      contentText = data.content || '';
    }

    // 检查并移除已存在的版权声明，避免重复
    contentText = contentText.replace(/本网站作品著作权归作者本人所有，凡发表在网站的文章，未经作者本人授权，不得转载。/g, '').trim();

    // 处理正文段落和图片的均匀分布
    const paragraphs = contentText.split('\n').filter(p => p.trim());
    const images = data.images || [];

    if (images.length > 0 && paragraphs.length > 0) {
      // 计算图片插入位置，让图片均匀分布在段落之间
      const totalSlots = paragraphs.length + 1; // 段落前后都可以插入图片
      const imagePositions: number[] = [];

      if (images.length >= totalSlots) {
        // 图片数量多于或等于可插入位置，每个位置都插入
        for (let i = 0; i < totalSlots; i++) {
          imagePositions.push(i);
        }
      } else {
        // 图片数量少于可插入位置，均匀分布
        const step = totalSlots / images.length;
        for (let i = 0; i < images.length; i++) {
          const position = Math.floor(i * step);
          imagePositions.push(position);
        }
      }

      // 构建内容，交替插入段落和图片
      let imageIndex = 0;
      for (let i = 0; i <= paragraphs.length; i++) {
        // 检查是否需要在这个位置插入图片
        if (imagePositions.includes(i) && imageIndex < images.length) {
          const proxiedImg = processImageUrl(images[imageIndex]);
          richContent += `
          <div style="text-align: center; margin: 25px 0;">
            <img src="${proxiedImg}" alt="图片 ${imageIndex + 1}" style="
              max-width: 100%;
              height: auto;
            " />
          </div>`;
          imageIndex++;
        }

        // 插入段落（如果不是最后一个位置）
        if (i < paragraphs.length) {
          richContent += `
          <p style="
            margin: 15px 0;
            font-size: 16px;
            line-height: 1.8;
            color: #333;
            text-indent: 0;
          ">${paragraphs[i].trim()}</p>`;
        }
      }

      // 如果还有剩余图片，添加到最后
      while (imageIndex < images.length) {
        const proxiedImg = processImageUrl(images[imageIndex]);
        richContent += `
          <div style="text-align: center; margin: 25px 0;">
            <img src="${proxiedImg}" alt="图片 ${imageIndex + 1}" style="
              max-width: 100%;
              height: auto;
            " />
          </div>`;
        imageIndex++;
      }
    } else {
      // 没有图片时，只添加段落
      paragraphs.forEach(paragraph => {
        richContent += `
          <p style="
            margin: 15px 0;
            font-size: 16px;
            line-height: 1.8;
            color: #333;
            text-indent: 0;
          ">${paragraph.trim()}</p>`;
      });
    }

    // 版权声明
    richContent += `
          <p style="
            margin: 30px 0 15px 0;
            font-size: 14px;
            color: #999;
            text-align: left;
            text-indent: 0;
            border-top: 1px solid #eee;
            padding-top: 15px;
          ">本网站作品著作权归作者本人所有，凡发表在网站的文章，未经作者本人授权，不得转载。</p>
        </div>`;

    // 编者按和作者简介区域
    if (data.editorNote || data.authorBio) {
      richContent += `
        <div style="
          background: #fff;
          padding: 25px;
          margin: 25px 0;
          border-radius: 12px;
          border: 2px solid #e9ecef;
          box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        ">`;

      // 编者按
      if (data.editorNote) {
        // 清理编者按内容，去掉重复的标题
        let cleanEditorNote = data.editorNote;
        cleanEditorNote = cleanEditorNote.replace(/^【?编者按】?[：:]?\s*/i, '');
        cleanEditorNote = cleanEditorNote.replace(/^编者按[：:]?\s*/i, '');

        richContent += `
          <div style="margin-bottom: 20px;">
            <h3 style="
              font-size: 18px;
              font-weight: bold;
              color: #333;
              margin: 0 0 10px 0;
              border-bottom: 2px solid ${style.borderColor};
              padding-bottom: 5px;
              display: inline-block;
            ">编者按</h3>
            <p style="
              margin: 0;
              font-size: 15px;
              color: #555;
              line-height: 1.7;
              text-indent: 0;
            ">${cleanEditorNote}</p>
          </div>`;
      }

      // 作者简介
      if (data.authorBio) {
        // 清理作者简介内容，去掉重复的"作者："
        let cleanAuthorBio = data.authorBio;
        cleanAuthorBio = cleanAuthorBio.replace(/^作者[：:]?\s*/i, '');

        richContent += `
          <div>
            <h3 style="
              font-size: 18px;
              font-weight: bold;
              color: #333;
              margin: 0 0 15px 0;
              border-bottom: 2px solid ${style.borderColor};
              padding-bottom: 5px;
              display: inline-block;
            ">作者简介</h3>
            <div style="display: flex; align-items: flex-start; gap: 15px;">`;

        if (data.authorPhoto) {
          const proxiedPhoto = processImageUrl(data.authorPhoto);
          richContent += `
              <img src="${proxiedPhoto}" alt="${data.author}" style="
                width: 80px;
                height: 80px;
                border-radius: 50%;
                object-fit: cover;
                flex-shrink: 0;
              " />`;
        }

        richContent += `
              <div>
                <p style="
                  margin: 0 0 5px 0;
                  font-size: 16px;
                  font-weight: bold;
                  color: #333;
                ">${data.author}</p>
                <p style="
                  margin: 0;
                  font-size: 15px;
                  color: #555;
                  line-height: 1.7;
                  text-indent: 0;
                ">${cleanAuthorBio}</p>
              </div>
            </div>
          </div>`;
      }

      richContent += `</div>`;
    }

    richContent += `</div>`;

    return richContent;
  };

  const handleUrlExtract = async () => {
    if (!url) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/extract', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        throw new Error('提取失败');
      }

      const data = await response.json();

      // 转换内容为富文本格式
      const richContent = convertToRichText(data);
      const processedData = {
        ...data,
        content: richContent
      };

      setExtractedData(processedData);
    } catch (error) {
      console.error('提取失败:', error);
      alert('提取失败，请检查URL是否有效');
    } finally {
      setIsLoading(false);
    }
  };

  const handleContentPaste = () => {
    if (!content) return;

    // 将粘贴的内容转换为结构化数据
    const lines = content.split('\n').filter(line => line.trim());
    const title = lines[0] || '粘贴的文章';
    const contentText = lines.slice(1).join('\n');

    const pastedData = {
      title,
      author: '用户输入',
      summary: contentText.substring(0, 200) + '...',
      content: contentText,
      images: [],
      editorNote: ''
    };

    // 转换为富文本格式
    const richContent = convertToRichText(pastedData);
    const processedData = {
      ...pastedData,
      content: richContent
    };

    setExtractedData(processedData);
  };

  const handleAutoFormat = async () => {
    if (!extractedData?.content) {
      alert('请先提取或输入内容');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/format', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: extractedData.content,
          styleId: selectedStyle
        }),
      });

      if (!response.ok) {
        throw new Error('格式化失败');
      }

      const data = await response.json();
      setFormattedContent(data.formattedContent);

      // 更新提取数据中的内容为格式化后的内容
      setExtractedData({
        ...extractedData,
        content: data.formattedContent
      });

      alert(`已应用 ${data.styleName} 样式`);
    } catch (error) {
      console.error('自动排版失败:', error);
      alert('自动排版失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOriginalityCheck = async () => {
    if (!extractedData?.content) {
      alert('请先提取或输入内容');
      return;
    }

    setIsCheckingOriginality(true);
    try {
      const response = await fetch('/api/originality', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: extractedData.content,
          title: extractedData.title || ''
        }),
      });

      if (!response.ok) {
        throw new Error('原创检测失败');
      }

      const result = await response.json();
      setOriginalityResult(result);
    } catch (error) {
      console.error('原创检测失败:', error);
      alert('原创检测失败，请重试');
    } finally {
      setIsCheckingOriginality(false);
    }
  };

  const handlePublishToWechat = () => {
    // TODO: 实现微信公众号发布
    console.log('发布到微信公众号');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            微信公众号自动排版工具
          </h1>
          <p className="text-lg text-gray-600">
            智能提取、自动排版、原创检测、一键发布
          </p>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-10 gap-8">
          {/* 左侧：输入和编辑区域 (7/10) */}
          <div className="lg:col-span-7 space-y-6">
            {/* 标签页导航 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="flex border-b border-gray-200">
                <button
                  onClick={() => setActiveTab('url')}
                  className={`flex-1 px-4 py-3 text-sm font-medium ${
                    activeTab === 'url'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  URL导入
                </button>
                <button
                  onClick={() => setActiveTab('paste')}
                  className={`flex-1 px-4 py-3 text-sm font-medium ${
                    activeTab === 'paste'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  文本粘贴
                </button>
                <button
                  onClick={() => setActiveTab('editor')}
                  className={`flex-1 px-4 py-3 text-sm font-medium ${
                    activeTab === 'editor'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  富文本编辑
                </button>
              </div>

              <div className="p-6">
                {/* URL输入 */}
                {activeTab === 'url' && (
                  <div className="space-y-4">
                    <input
                      type="url"
                      value={url}
                      onChange={(e) => setUrl(e.target.value)}
                      placeholder="请输入文章URL"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <button
                      onClick={handleUrlExtract}
                      disabled={!url || isLoading}
                      className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                      {isLoading ? '提取中...' : '提取内容'}
                    </button>
                  </div>
                )}

                {/* 内容粘贴 */}
                {activeTab === 'paste' && (
                  <div className="space-y-4">
                    <textarea
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                      placeholder="请粘贴文章内容"
                      rows={8}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    />
                    <button
                      onClick={handleContentPaste}
                      disabled={!content}
                      className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                      处理内容
                    </button>
                  </div>
                )}

                {/* 富文本编辑器 */}
                {activeTab === 'editor' && (
                  <div className="space-y-4">
                    <RichTextEditor
                      content={richTextContent}
                      onChange={setRichTextContent}
                      placeholder="请输入或粘贴文章内容（支持图片、链接等富文本格式）"
                    />
                    <button
                      onClick={() => {
                        // 将富文本内容转换为提取数据格式
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = richTextContent;
                        const textContent = tempDiv.textContent || tempDiv.innerText || '';

                        setExtractedData({
                          title: '富文本编辑内容',
                          author: '用户输入',
                          summary: textContent.substring(0, 200) + '...',
                          content: richTextContent,
                          images: [],
                          editorNote: ''
                        });
                      }}
                      disabled={!richTextContent}
                      className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                      使用富文本内容
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* 文章编辑器 */}
            {extractedData && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">文章编辑器</h2>
                  <div className="flex items-center space-x-4">
                    <div className="flex space-x-2 text-sm text-gray-600">
                      <span>作者：{extractedData.author}</span>
                      {extractedData.images && extractedData.images.length > 0 && (
                        <span>图片：{extractedData.images.length}张</span>
                      )}
                    </div>
                    <div className="flex bg-gray-100 rounded-lg p-1">
                      <button
                        onClick={() => setIsPreviewMode(false)}
                        className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                          !isPreviewMode
                            ? 'bg-white text-blue-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        编辑
                      </button>
                      <button
                        onClick={() => setIsPreviewMode(true)}
                        className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                          isPreviewMode
                            ? 'bg-white text-blue-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        预览
                      </button>
                    </div>
                  </div>
                </div>

                {/* 统一的富文本编辑器 */}
                <div>
                  {isPreviewMode ? (
                    <div className="border border-gray-200 rounded-lg bg-white">
                      <div className="bg-gray-50 px-4 py-2 border-b border-gray-200 rounded-t-lg">
                        <span className="text-sm font-medium text-gray-700">预览模式</span>
                      </div>
                      <div
                        className="prose prose-lg max-w-none p-6 min-h-[500px]"
                        style={{
                          fontFamily: '"PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif',
                          lineHeight: '1.8',
                          color: '#333'
                        }}
                        dangerouslySetInnerHTML={{ __html: extractedData.content }}
                      />
                    </div>
                  ) : (
                    <RichTextEditor
                      content={extractedData.content}
                      onChange={(content) => setExtractedData({...extractedData, content})}
                      placeholder="请输入完整的文章内容，包括标题、副标题、作者、摘要、正文、编者按等..."
                    />
                  )}
                </div>
              </div>
            )}
          </div>

          {/* 右侧：排版样式和操作区域 (3/10) */}
          <div className="lg:col-span-3 space-y-6">
            {/* 排版样式选择 */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">排版样式</h2>
              <div className="space-y-3">
                <select
                  value={selectedStyle}
                  onChange={(e) => setSelectedStyle(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {availableStyles.map(style => (
                    <option key={style.id} value={style.id}>
                      {style.name} - {style.description}
                    </option>
                  ))}
                </select>
                <button
                  onClick={handleAutoFormat}
                  disabled={!extractedData?.content || isLoading}
                  className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  {isLoading ? '排版中...' : '应用排版样式'}
                </button>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">操作</h2>
              <div className="space-y-3">
                <button
                  onClick={handleOriginalityCheck}
                  disabled={!extractedData?.content || isCheckingOriginality}
                  className="w-full bg-orange-600 text-white py-2 px-4 rounded-md hover:bg-orange-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  {isCheckingOriginality ? '检测中...' : '原创检测'}
                </button>
                <button
                  onClick={handlePublishToWechat}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700"
                >
                  发布到微信公众号
                </button>
                <button
                  onClick={async () => {
                    if (extractedData?.content) {
                      try {
                        // 创建一个临时的div来处理HTML内容
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = extractedData.content;

                        // 处理图片：将代理URL转换回原始URL
                        const images = tempDiv.querySelectorAll('img');
                        images.forEach(img => {
                          const src = img.src;
                          if (src.includes('/api/image-proxy?url=')) {
                            const originalUrl = decodeURIComponent(src.split('url=')[1]);
                            img.src = originalUrl;
                          }
                        });

                        // 创建富文本内容用于复制
                        const htmlContent = tempDiv.innerHTML;
                        const textContent = tempDiv.textContent || tempDiv.innerText || '';

                        // 使用现代的Clipboard API复制富文本
                        if (navigator.clipboard && window.ClipboardItem) {
                          const clipboardItem = new ClipboardItem({
                            'text/html': new Blob([htmlContent], { type: 'text/html' }),
                            'text/plain': new Blob([textContent], { type: 'text/plain' })
                          });
                          await navigator.clipboard.write([clipboardItem]);
                          alert('富文本内容（包含图片）已复制到剪贴板，可直接粘贴到微信公众号编辑器');
                        } else {
                          // 降级方案：只复制文本
                          await navigator.clipboard.writeText(textContent);
                          alert('文本内容已复制到剪贴板');
                        }
                      } catch (error) {
                        console.error('复制失败:', error);
                        // 最后的降级方案
                        const textContent = extractedData.content.replace(/<[^>]*>/g, '');
                        navigator.clipboard.writeText(textContent);
                        alert('已复制纯文本内容到剪贴板');
                      }
                    }
                  }}
                  disabled={!extractedData?.content}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  复制富文本内容
                </button>
                <button
                  onClick={async () => {
                    if (extractedData?.content) {
                      try {
                        // 生成兼容性更好的富文本内容
                        const compatibleContent = convertToCompatibleRichText(extractedData);

                        // 创建临时div来处理内容
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = compatibleContent;

                        // 创建富文本内容用于复制
                        const htmlContent = tempDiv.innerHTML;
                        const textContent = tempDiv.textContent || tempDiv.innerText || '';

                        // 使用现代的Clipboard API复制富文本
                        if (navigator.clipboard && window.ClipboardItem) {
                          const clipboardItem = new ClipboardItem({
                            'text/html': new Blob([htmlContent], { type: 'text/html' }),
                            'text/plain': new Blob([textContent], { type: 'text/plain' })
                          });
                          await navigator.clipboard.write([clipboardItem]);
                          alert('兼容模式富文本内容已复制到剪贴板，可直接粘贴到其他公众号编辑器');
                        } else {
                          // 降级方案：只复制文本
                          await navigator.clipboard.writeText(textContent);
                          alert('文本内容已复制到剪贴板');
                        }
                      } catch (error) {
                        console.error('复制失败:', error);
                        // 最后的降级方案
                        const textContent = extractedData.content.replace(/<[^>]*>/g, '');
                        navigator.clipboard.writeText(textContent);
                        alert('已复制纯文本内容到剪贴板');
                      }
                    }
                  }}
                  disabled={!extractedData?.content}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  复制兼容模式内容
                </button>
              </div>
            </div>

            {/* 原创检测结果 */}
            {originalityResult && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold mb-4">原创检测结果</h2>
                <div className="space-y-4">
                  {/* 检测状态 */}
                  <div className={`p-4 rounded-lg ${
                    originalityResult.isOriginal
                      ? 'bg-green-50 border border-green-200'
                      : 'bg-red-50 border border-red-200'
                  }`}>
                    <div className="flex items-center justify-between">
                      <span className={`font-semibold ${
                        originalityResult.isOriginal ? 'text-green-800' : 'text-red-800'
                      }`}>
                        {originalityResult.isOriginal ? '✅ 原创内容' : '❌ 疑似非原创'}
                      </span>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        originalityResult.riskLevel === 'low'
                          ? 'bg-green-100 text-green-800'
                          : originalityResult.riskLevel === 'medium'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {originalityResult.riskLevel === 'low' ? '低风险' :
                         originalityResult.riskLevel === 'medium' ? '中风险' : '高风险'}
                      </span>
                    </div>
                  </div>

                  {/* 相似度信息 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="text-sm text-gray-600">相似度</div>
                      <div className="text-2xl font-bold text-gray-900">
                        {originalityResult.similarity}%
                      </div>
                    </div>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="text-sm text-gray-600">重复文章数</div>
                      <div className="text-2xl font-bold text-gray-900">
                        {originalityResult.duplicateCount}
                      </div>
                    </div>
                  </div>

                  {/* 建议 */}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">检测建议</h3>
                    <ul className="space-y-1">
                      {originalityResult.suggestions.map((suggestion: string, index: number) => (
                        <li key={index} className="text-sm text-gray-700 flex items-start">
                          <span className="mr-2">•</span>
                          <span>{suggestion}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* 预览区域 */}
            {formattedContent && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold mb-4">排版预览</h2>
                <div
                  className="border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto"
                  dangerouslySetInnerHTML={{ __html: formattedContent }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
